from flask import Flask, send_file, request, g
from flask_sqlalchemy import SQLAlchemy
from flask_login import LoginManager
from flask_admin import Admin
from flask_migrate import Migrate
from flask_cors import CORS
from flask_wtf.csrf import CSRFProtect
import os
import logging
import secrets
from config import Config
from app.utils.filters import register_filters

# 初始化扩展
db = SQLAlchemy()
login_manager = LoginManager()
flask_admin = Admin(name='系统管理', template_mode='bootstrap4')  # 重命名避免冲突
migrate = Migrate()
csrf = CSRFProtect()

def create_app(config_class=Config):
    app = Flask(__name__, static_folder='static')

    # 配置应用
    app.config.from_object(config_class)

    # 确保实例文件夹存在
    try:
        os.makedirs(app.instance_path)
    except OSError:
        pass

    # 初始化扩展
    # 配置数据库连接池
    from sqlalchemy import pool
    app.config['SQLALCHEMY_ENGINE_OPTIONS'] = {
        'pool_size': 10,  # 连接池大小
        'max_overflow': 20,  # 最大溢出连接数
        'pool_timeout': 30,  # 连接超时时间（秒）
        'pool_recycle': 1800,  # 连接回收时间（秒）
        'pool_pre_ping': True  # 连接前ping，确保连接有效
    }

    db.init_app(app)
    login_manager.init_app(app)
    login_manager.login_view = 'auth.login'

    # 初始化学校中间件
    from app.utils.school_middleware import SchoolMiddleware
    school_middleware = SchoolMiddleware(app)

    # 设置学校中间件豁免的路由
    school_middleware.exempt('/static/')
    school_middleware.exempt('/favicon.ico')
    school_middleware.exempt('/login')
    school_middleware.exempt('/logout')
    school_middleware.exempt('/auth/')
    school_middleware.exempt('/admin/')
    school_middleware.exempt('/api/')
    school_middleware.exempt('/error/')
    school_middleware.exempt('/main/')
    school_middleware.exempt('/system-fix/')

    # 添加SQLAlchemy事件监听器，处理datetime字段的精度问题
    from sqlalchemy import event
    from sqlalchemy.orm import mapper
    from datetime import datetime

    @event.listens_for(mapper, 'before_insert')
    def before_insert(mapper, connection, instance):
        """在插入前处理datetime字段的精度问题"""
        from datetime import date
        for key, value in instance.__dict__.items():
            if isinstance(value, datetime):
                # 检查字段类型，如果是date字段，只保留日期部分
                if hasattr(instance.__class__, key):
                    column = getattr(instance.__class__, key)
                    if hasattr(column.property, 'columns') and column.property.columns:
                        column_type = str(column.property.columns[0].type)
                        if 'DATE' in column_type.upper() and 'DATETIME' not in column_type.upper():
                            # 这是一个DATE字段，转换为date对象
                            setattr(instance, key, value.date())
                        else:
                            # 这是一个DATETIME字段，去除微秒并保持datetime类型
                            setattr(instance, key, value.replace(microsecond=0))
            elif isinstance(value, date) and not isinstance(value, datetime):
                # 如果已经是date对象，保持不变
                pass

    # 尝试从系统设置中获取项目名称
    try:
        from app.models_system import SystemSetting
        project_name = SystemSetting.get_value('project_name', '智慧食堂平台')
        flask_admin.name = project_name
    except:
        pass

    flask_admin.init_app(app)
    migrate.init_app(app, db)
    csrf.init_app(app)
    CORS(app)

    # 为财务模块设置CSRF豁免
    from app.routes.financial import financial_bp
    csrf.exempt(financial_bp)
    app.register_blueprint(financial_bp)

    # 为所有API路由设置CSRF豁免
    from app.api import api_bp
    csrf.exempt(api_bp)

    # 数据库结构更新应该通过迁移脚本进行，而不是在应用启动时动态修改

    # 注册过滤器
    register_filters(app)

    # 注册 Jinja2 扩展
    from app.utils.jinja_extensions import register_extensions
    register_extensions(app)

    # 注册蓝图
    from app.auth import auth_bp
    app.register_blueprint(auth_bp)

    from app.main import main_bp
    app.register_blueprint(main_bp)

    from app.api import api_bp
    app.register_blueprint(api_bp, url_prefix='/api')

    # 注册周菜谱展示接口
    from app.api.menu_display import menu_display_bp
    app.register_blueprint(menu_display_bp, url_prefix='/api/menu-display')

    # 注册灵活采购系统接口
    from app.api.flexible_purchase import flexible_purchase_bp
    app.register_blueprint(flexible_purchase_bp, url_prefix='/api/flexible-purchase')

    from app.employee import employee_bp
    app.register_blueprint(employee_bp, url_prefix='/employee')

    from app.area import area_bp
    app.register_blueprint(area_bp, url_prefix='/area')

    from app.admin import system_bp, admin_bp
    app.register_blueprint(system_bp, url_prefix='/admin')
    app.register_blueprint(admin_bp, url_prefix='/admin')

    from app.notification import notification_bp
    app.register_blueprint(notification_bp)

    # 注册功能相关蓝图
    from app.routes.ingredient_category import ingredient_category_bp
    app.register_blueprint(ingredient_category_bp, url_prefix='/ingredient-category')

    from app.routes.ingredient import ingredient_bp
    app.register_blueprint(ingredient_bp, url_prefix='/ingredient')

    from app.routes.recipe_category import recipe_category_bp
    app.register_blueprint(recipe_category_bp, url_prefix='/recipe-category')

    from app.routes.recipe import recipe_bp
    app.register_blueprint(recipe_bp, url_prefix='/recipe')

    from app.routes.recipe_favorite import recipe_favorite_bp
    app.register_blueprint(recipe_favorite_bp, url_prefix='/recipe-favorite')

    from app.routes.recipe_rating import recipe_rating_bp
    app.register_blueprint(recipe_rating_bp)



    # 注册周菜单V2模块蓝图（新版本）
    from app.routes.weekly_menu_v2 import weekly_menu_v2_bp
    app.register_blueprint(weekly_menu_v2_bp)

    # 添加旧版本URL重定向到新版本
    from flask import redirect, url_for

    @app.route('/weekly-menu')
    def redirect_weekly_menu_index():
        return redirect(url_for('weekly_menu_v2.index'))

    @app.route('/weekly-menu/plan')
    def redirect_weekly_menu_plan():
        return redirect(url_for('weekly_menu_v2.plan'))

    @app.route('/weekly-menu/<int:id>')
    def redirect_weekly_menu_view(id):
        return redirect(url_for('weekly_menu_v2.view', id=id))

    # 注册周菜单API蓝图
    from app.routes.api_weekly_menu import api_weekly_menu_bp
    app.register_blueprint(api_weekly_menu_bp)

    # 注册菜单同步模块蓝图
    from app.routes.menu_sync import menu_sync_bp
    app.register_blueprint(menu_sync_bp)

    # 注册数据修复模块蓝图
    from app.routes.data_repair import data_repair_bp
    app.register_blueprint(data_repair_bp)

    # 注册采购订单蓝图
    from app.routes.purchase_order import purchase_order_bp
    app.register_blueprint(purchase_order_bp)

    from app.routes.food_sample import food_sample_bp
    app.register_blueprint(food_sample_bp)

    from app.routes.consumption_plan import consumption_plan_bp
    app.register_blueprint(consumption_plan_bp)

    # 注册消耗计划超级编辑器蓝图
    from app.routes.consumption_plan_super import consumption_plan_super_bp
    app.register_blueprint(consumption_plan_super_bp)

    # 注册食材溯源与留样模块蓝图
    from app.routes.food_trace import food_trace_bp
    app.register_blueprint(food_trace_bp)

    # 注册食材溯源API蓝图
    from app.routes.api import api_bp
    app.register_blueprint(api_bp, url_prefix='/food-trace-api')

    from app.routes.inventory_alert import inventory_alert_bp
    app.register_blueprint(inventory_alert_bp)

    from app.routes.stock_out import stock_out_bp
    app.register_blueprint(stock_out_bp)

    from app.routes.warehouse import warehouse_bp
    app.register_blueprint(warehouse_bp)

    from app.routes.storage_location import storage_location_bp
    app.register_blueprint(storage_location_bp)

    from app.routes.stock_in import stock_in_bp
    app.register_blueprint(stock_in_bp)

    # 注册入库向导蓝图
    from app.routes.stock_in_wizard import stock_in_wizard_bp
    app.register_blueprint(stock_in_wizard_bp)

    # 注册入库食材详情蓝图
    from app.routes.stock_in_detail import stock_in_detail_bp
    app.register_blueprint(stock_in_detail_bp)

    from app.routes.inventory import inventory_bp
    app.register_blueprint(inventory_bp)

    # 注册食材溯源模块蓝图
    from app.routes.material_batch import material_batch_bp
    app.register_blueprint(material_batch_bp)

    from app.routes.trace_document import trace_document_bp
    app.register_blueprint(trace_document_bp)

    from app.routes.batch_flow import batch_flow_bp
    app.register_blueprint(batch_flow_bp)

    from app.routes.traceability import traceability_bp
    app.register_blueprint(traceability_bp)

    # 注册食材溯源API蓝图
    from app.routes.traceability_api import traceability_api_bp
    app.register_blueprint(traceability_api_bp)

    # 注册食材溯源API列表蓝图
    from app.routes.traceability_api_lists import traceability_api_lists_bp
    app.register_blueprint(traceability_api_lists_bp)

    # 添加状态样式过滤器
    @app.template_filter('status_class')
    def status_class_filter(status):
        status_classes = {
            '计划中': 'secondary',
            '已审核': 'info',
            '已执行': 'success',
            '已取消': 'danger',
            '已发布': 'danger',  # 改为红色，更加醒目
            '待审核': 'warning',
            '已出库': 'success'
        }
        return status_classes.get(status, 'secondary')

    # 注册食堂日常管理模块蓝图
    from app.routes.daily_management import daily_management_bp
    app.register_blueprint(daily_management_bp)

    # 注册食堂日常管理增强API蓝图
    from app.routes.daily_management.enhanced_api import bp as daily_management_enhanced_api_bp
    app.register_blueprint(daily_management_enhanced_api_bp)

    # 注册二维码API蓝图
    from app.api.qr_code import qr_code_bp
    app.register_blueprint(qr_code_bp)

    # 注册周菜单临时菜品API蓝图 - 暂时注释，因为相关模型不存在
    # from app.api.weekly_menu_temp import weekly_menu_temp_bp
    # app.register_blueprint(weekly_menu_temp_bp)

    # 注册仪表盘API蓝图
    from app.routes.dashboard_api import dashboard_api_bp
    app.register_blueprint(dashboard_api_bp)

    # 注册用户引导API蓝图
    from app.api.guide_routes import guide_api_bp
    app.register_blueprint(guide_api_bp)

    # 注册引导管理蓝图
    from app.admin.guide_management_routes import guide_mgmt_bp
    app.register_blueprint(guide_mgmt_bp)

    # 初始化安全配置
    from app.security_config import init_security
    init_security(app)

    # 注册安全管理蓝图
    from app.routes.security_admin import security_admin_bp
    app.register_blueprint(security_admin_bp)

    # 注册入库检查模块蓝图
    from app.routes.inspection import inspection_bp
    app.register_blueprint(inspection_bp, url_prefix='/inspection')

    # 注册供应商管理模块蓝图
    from app.register_supplier_blueprints import register_supplier_blueprints
    register_supplier_blueprints(app)

    # 注册错误处理
    from app.errors import register_error_handlers
    register_error_handlers(app)

    # 注册系统修复工具蓝图
    from app.system_fix import system_fix_bp, role_permissions_fix_bp, permission_migration_bp, permission_audit_bp
    app.register_blueprint(system_fix_bp, url_prefix='/system-fix')
    app.register_blueprint(role_permissions_fix_bp, url_prefix='/role-permissions-fix')
    app.register_blueprint(permission_migration_bp, url_prefix='/permission-migration')
    app.register_blueprint(permission_audit_bp, url_prefix='/permission-audit')

    # 注册超级删除功能蓝图
    from app.admin.super_delete_routes import super_delete_bp
    app.register_blueprint(super_delete_bp)

    # 注册采购员角色修复蓝图
    from app.admin.fix_purchase_role import fix_purchase_bp
    app.register_blueprint(fix_purchase_bp, url_prefix='/fix-purchase')

    # 注册学校管理员用户管理蓝图
    from app.school_admin import school_admin_bp
    app.register_blueprint(school_admin_bp, url_prefix='/school-admin')

    # 注册示例页面蓝图
    from app.routes.examples import examples_bp
    app.register_blueprint(examples_bp)

    # 注册在线咨询模块蓝图
    from app.consultation import bp as consultation_bp
    app.register_blueprint(consultation_bp, url_prefix='/consultation')

    # 注册在线咨询API蓝图（独立路由）
    try:
        from app.consultation_api import consultation_api_bp
        app.register_blueprint(consultation_api_bp)
        app.logger.info("在线咨询API蓝图注册成功")
    except Exception as e:
        app.logger.warning(f"在线咨询API蓝图注册失败: {str(e)}")

    # 注册首页轮播图管理蓝图
    try:
        from app.routes.homepage_carousel import homepage_carousel_bp
        app.register_blueprint(homepage_carousel_bp)
        app.logger.info("首页轮播图管理蓝图注册成功")
    except Exception as e:
        app.logger.warning(f"首页轮播图管理蓝图注册失败: {str(e)}")

    # 财务管理模块蓝图已在前面注册并设置了CSRF豁免

    # 注册帮助中心模块蓝图
    try:
        from app.routes.help import help_bp
        app.register_blueprint(help_bp)
        app.logger.info("帮助中心模块蓝图注册成功")
    except Exception as e:
        app.logger.warning(f"帮助中心模块蓝图注册失败: {str(e)}")

    # 注册命令
    from app.commands import register_commands
    register_commands(app)

    # 使用Flask-WTF的CSRF保护，移除自定义CSRF保护

    # 确保上传目录存在
    os.makedirs(os.path.join(app.static_folder, 'uploads/recipes/processes'), exist_ok=True)
    os.makedirs(os.path.join(app.static_folder, 'uploads/recipe_reviews'), exist_ok=True)
    os.makedirs(os.path.join(app.static_folder, 'uploads/trace_documents'), exist_ok=True)

    # 确保 PDF 和字体目录存在
    os.makedirs(os.path.join(app.static_folder, 'pdf/purchase_orders'), exist_ok=True)
    os.makedirs(os.path.join(app.static_folder, 'fonts'), exist_ok=True)

    # 复制系统字体到应用字体目录
    system_font = 'C:\\Windows\\Fonts\\simsun.ttc'
    app_font = os.path.join(app.static_folder, 'fonts', 'simsun.ttf')
    if os.path.exists(system_font) and not os.path.exists(app_font):
        import shutil
        try:
            shutil.copy2(system_font, app_font)
            app.logger.info(f"Copied system font to: {app_font}")
        except Exception as e:
            app.logger.error(f"Failed to copy system font: {str(e)}")

    # 确保食堂日常管理模块上传目录存在
    os.makedirs(os.path.join(app.static_folder, 'uploads/daily_management/inspection'), exist_ok=True)
    os.makedirs(os.path.join(app.static_folder, 'uploads/daily_management/companion'), exist_ok=True)
    os.makedirs(os.path.join(app.static_folder, 'uploads/daily_management/training'), exist_ok=True)
    os.makedirs(os.path.join(app.static_folder, 'uploads/daily_management/event'), exist_ok=True)
    os.makedirs(os.path.join(app.static_folder, 'uploads/daily_management/issue'), exist_ok=True)

    # 确保引导视频上传目录存在
    os.makedirs(os.path.join(app.static_folder, 'videos/guide'), exist_ok=True)

    # 导入食谱高级功能模型
    try:
        from app.models_recipe_advanced import (
            RecipeReview, RecipeReviewImage, RecipeReviewTag, RecipeImprovementSuggestion,
            RecipeVersion, RecipeIngredientAlternative, RecipeSeasonalInfo,
            RecipeTag, UserRecipeFavorite, UserSearchHistory
        )
    except ImportError:
        app.logger.warning("食谱高级功能模型导入失败，可能尚未创建相关模型文件")

    # 导入食材溯源模块模型
    try:
        from app.models_ingredient_traceability import (
            MaterialBatch, TraceDocument, BatchFlow
        )
    except ImportError:
        app.logger.warning("食材溯源模块模型导入失败，可能尚未创建相关模型文件")

    # 导入系统设置模型
    try:
        from app.models_system import SystemSetting, DatabaseBackup, SystemLog
    except ImportError:
        app.logger.warning("系统设置模型导入失败，可能尚未创建相关模型文件")

    # 导入食堂日常管理模块模型
    try:
        from app.models_daily_management import (
            DailyLog, InspectionRecord, DiningCompanion,
            CanteenTrainingRecord, SpecialEvent, Issue, Photo
        )
    except ImportError:
        app.logger.warning("食堂日常管理模块模型导入失败，可能尚未创建相关模型文件")

    # 导入产品批量上架模块模型
    try:
        from app.models_product_batch import (
            StandardUnit, CategoryUnitMapping, ProductBatch
        )
    except ImportError:
        app.logger.warning("产品批量上架模块模型导入失败，可能尚未创建相关模型文件")

    # 导入周菜单临时菜品模型
    try:
        from app.models_weekly_menu_temp import WeeklyMenuRecipesTemp
        app.logger.info("周菜单临时菜品模型导入成功")
    except ImportError:
        app.logger.warning("周菜单临时菜品模型导入失败，可能尚未创建相关模型文件")

    # 导入在线咨询模型
    try:
        from app.models_consultation import OnlineConsultation
        app.logger.info("在线咨询模型导入成功")
    except ImportError:
        app.logger.warning("在线咨询模型导入失败，可能尚未创建相关模型文件")

    # 导入首页轮播图模型
    try:
        from app.models_homepage_carousel import HomepageCarousel
        app.logger.info("首页轮播图模型导入成功")
    except ImportError:
        app.logger.warning("首页轮播图模型导入失败，可能尚未创建相关模型文件")

    # 导入财务系统模型
    try:
        from app.models_financial import (
            AccountingSubject, FinancialVoucher, VoucherDetail,
            AccountPayable, PaymentRecord
        )
        app.logger.info("财务系统模型导入成功")
    except ImportError:
        app.logger.warning("财务系统模型导入失败，可能尚未创建相关模型文件")

    # 添加自定义过滤器
    import json
    from datetime import datetime, date

    # 自定义JSON编码器，处理datetime对象
    class DateTimeJSONEncoder(json.JSONEncoder):
        def default(self, obj):
            if isinstance(obj, (datetime, date)):
                return obj.isoformat()
            return super().default(obj)

    @app.template_filter('tojson')
    def to_json(value):
        return json.dumps(value, cls=DateTimeJSONEncoder)

    @app.template_filter('fromjson')
    def from_json(value):
        try:
            if isinstance(value, str):
                return json.loads(value)
            elif isinstance(value, dict):
                return value
            else:
                return {}
        except:
            return {}

    @app.template_filter('nl2br')
    def nl2br_filter(text):
        if text:
            return text.replace('\n', '<br>')
        return ''

    @app.template_test('contains')
    def contains_test(value, other):
        if value is None:
            return 0
        return other in value

    # 注册权限相关的模板过滤器
    from app.utils.permissions import get_permission_chinese_name, get_module_chinese_name

    @app.template_filter('permission_chinese_name')
    def permission_chinese_name_filter(value):
        """获取权限的中文名称"""
        if isinstance(value, tuple) and len(value) == 2:
            module, action = value
            return get_permission_chinese_name(module, action)
        return str(value)

    @app.template_filter('module_chinese_name')
    def module_chinese_name_filter(module):
        """获取模块的中文名称"""
        return get_module_chinese_name(module)

    @app.template_filter('format_datetime')
    def format_datetime_filter(value, format='%Y-%m-%d %H:%M'):
        """格式化日期时间，处理字符串和datetime对象，默认只精确到分钟"""
        from datetime import datetime, date

        # 处理空值
        if value is None:
            return ''

        # 处理日期对象
        if isinstance(value, date) and not isinstance(value, datetime):
            if '%H' in format or '%M' in format:
                # 如果格式包含时间部分，但值只是日期，则转换为日期时间
                value = datetime.combine(value, datetime.min.time())
            else:
                # 否则直接格式化日期
                return value.strftime(format)

        # 处理字符串
        if isinstance(value, str):
            try:
                # 尝试将字符串解析为日期时间对象
                if 'T' in value:  # ISO格式 2023-01-01T00:00:00
                    dt = datetime.fromisoformat(value)
                elif '-' in value and len(value) >= 10:  # 2023-01-01
                    if len(value) > 10 and ':' in value:  # 2023-01-01 12:34:56
                        if len(value) > 16:  # 包含秒
                            dt = datetime.strptime(value[:16], '%Y-%m-%d %H:%M')
                        else:
                            dt = datetime.strptime(value, '%Y-%m-%d %H:%M')
                    else:  # 只有日期部分
                        dt = datetime.strptime(value[:10], '%Y-%m-%d')
                        if '%H' in format or '%M' in format:
                            # 如果格式包含时间部分，则添加时间
                            dt = datetime.combine(dt.date(), datetime.min.time())
                else:
                    return value  # 无法解析的字符串直接返回

                # 确保时间只精确到分钟
                if isinstance(dt, datetime):
                    dt = dt.replace(second=0, microsecond=0)

                return dt.strftime(format)
            except (ValueError, TypeError):
                # 解析失败时返回原始字符串，但记录日志
                app.logger.warning(f"无法解析日期时间字符串: {value}")
                return value

        # 处理datetime对象
        if isinstance(value, datetime):
            # 确保时间只精确到分钟
            value = value.replace(second=0, microsecond=0)
            return value.strftime(format)

        # 其他类型转为字符串
        return str(value)

    # 添加上下文处理器
    from flask_login import current_user

    @app.context_processor
    def inject_user_permissions():
        """注入用户权限到模板上下文"""
        if current_user.is_authenticated:
            # 简单返回一个空列表，因为User模型中没有get_permissions方法
            return {'user_permissions': []}
        return {'user_permissions': []}

    @app.context_processor
    def inject_user_area():
        """安全地注入用户区域信息到模板上下文"""
        if current_user.is_authenticated:
            try:
                # 安全地获取用户区域信息，避免DetachedInstanceError
                if current_user.area_id:
                    from app.models import AdministrativeArea
                    area = db.session.query(AdministrativeArea).get(current_user.area_id)
                    if area:
                        return {
                            'current_user_area': area,
                            'current_user_area_name': area.name
                        }
                return {
                    'current_user_area': None,
                    'current_user_area_name': None
                }
            except Exception as e:
                app.logger.warning(f"获取用户区域信息失败: {str(e)}")
                return {
                    'current_user_area': None,
                    'current_user_area_name': None
                }
        return {
            'current_user_area': None,
            'current_user_area_name': None
        }

    @app.context_processor
    def inject_now():
        """注入当前时间到模板上下文，只精确到分钟"""
        from datetime import datetime, date
        now = datetime.now().replace(second=0, microsecond=0)
        # 返回一个函数，而不是直接返回datetime对象
        return {'now': lambda: int(datetime.now().timestamp()), 'date': date}

    # 不再需要自定义的CSRF令牌注入函数，Flask-WTF已经提供了csrf_token()函数
    # @app.context_processor
    # def inject_csrf_token():
    #     """注入CSRF令牌到模板上下文"""
    #     # 使用Flask-WTF的默认实现
    #     return {}

    @app.context_processor
    def inject_user_menu():
        """注入用户菜单配置到模板上下文 - 使用优雅菜单"""
        from app.utils.menu_elegant import get_elegant_user_menu, get_url

        if current_user.is_authenticated:
            user_menu = get_elegant_user_menu(current_user)
            return {'user_menu': user_menu, 'get_url': get_url}
        return {'user_menu': [], 'get_url': get_url}

    @app.context_processor
    def inject_project_name():
        """注入项目名称和系统设置到模板上下文"""
        try:
            from app.models_system import SystemSetting
            project_name = SystemSetting.get_value('project_name', '智慧食堂平台')
            theme_color = SystemSetting.get_value('theme_color', 'primary')
            system_logo = SystemSetting.get_value('system_logo', '')

            # 如果没有设置LOGO，使用默认LOGO
            if not system_logo:
                system_logo = '/static/uploads/system/logo_20250601185000.png'

            return {
                'project_name': project_name,
                'theme_color': theme_color,
                'system_logo': system_logo
            }
        except:
            return {
                'project_name': '智慧食堂平台',
                'theme_color': 'primary',
                'system_logo': '/static/uploads/system/logo_20250601185000.png'
            }

    @app.context_processor
    def inject_resources():
        """注入资源配置到模板上下文"""
        try:
            from app.config.resources import get_resource_urls
            return {'resources': get_resource_urls()}
        except Exception as e:
            app.logger.warning(f"资源配置注入失败: {str(e)}")
            # 返回默认的CDN资源配置
            return {
                'resources': {
                    'css': {
                        'tailwind': 'https://cdn.tailwindcss.com',
                        'fontawesome': 'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.7.2/css/all.min.css',
                        'bootstrap': 'https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css'
                    },
                    'js': {
                        'chartjs': 'https://cdn.jsdelivr.net/npm/chart.js@4.4.8/dist/chart.umd.min.js',
                        'jquery': 'https://code.jquery.com/jquery-3.7.1.min.js',
                        'bootstrap': 'https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js'
                    },
                    'fonts': {
                        'google_fonts': 'https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap'
                    },
                    'use_cdn': True
                }
            }

    @app.context_processor
    def inject_csp_nonce():
        """注入CSP nonce到模板上下文"""
        return {'csp_nonce': getattr(g, 'csp_nonce', '')}

    # 溯源功能暂时不需要考虑，移除重定向规则
    # 未来需要时可以重新添加

    # 生成CSP nonce的中间件
    @app.before_request
    def generate_csp_nonce():
        """为每个请求生成唯一的CSP nonce"""
        g.csp_nonce = secrets.token_urlsafe(16)

    # 添加缓存控制和安全头，为静态资源设置长期缓存，为动态内容禁用缓存
    @app.after_request
    def add_cache_control_and_security_headers(response):
        """为资源添加缓存控制头和安全头"""
        # 检查是否是动态内容（HTML页面或API响应）
        if 'text/html' in response.headers.get('Content-Type', '') or 'application/json' in response.headers.get('Content-Type', ''):
            # 动态内容不缓存
            response.headers['Cache-Control'] = 'no-store, no-cache, must-revalidate, max-age=0'
            response.headers['Pragma'] = 'no-cache'
            response.headers['Expires'] = '0'

            # 获取当前请求的nonce
            nonce = getattr(g, 'csp_nonce', None)

            # 添加Content Security Policy头
            # 由于nonce存在时unsafe-inline会被忽略，我们需要选择一种策略
            # 选项1：只使用unsafe-inline（移除nonce）以支持data-onclick事件处理器
            # 选项2：使用nonce但需要将所有事件处理器转为真正的脚本
            if nonce:
                # 暂时只使用unsafe-inline，因为我们的事件处理器需要它
                csp_policy = (
                    "default-src 'self'; "
                    "script-src 'self' 'unsafe-inline' 'unsafe-hashes' https: http:; "
                    "style-src 'self' 'unsafe-inline' https: http:; "
                    "img-src 'self' data: blob: https: http:; "
                    "font-src 'self' data: https: http:; "
                    "connect-src 'self' https: http:; "
                    "media-src 'self' https: http:; "
                    "object-src 'none'; "
                    "base-uri 'self'; "
                    "form-action 'self'; "
                    "frame-ancestors 'none'"
                )
            else:
                # 回退到unsafe-inline（如果nonce生成失败）
                csp_policy = (
                    "default-src 'self'; "
                    "script-src 'self' 'unsafe-inline' https: http:; "
                    "style-src 'self' 'unsafe-inline' https: http:; "
                    "img-src 'self' data: blob: https: http:; "
                    "font-src 'self' data: https: http:; "
                    "connect-src 'self' https: http:; "
                    "media-src 'self' https: http:; "
                    "object-src 'none'; "
                    "base-uri 'self'; "
                    "form-action 'self'; "
                    "frame-ancestors 'none'"
                )

            response.headers['Content-Security-Policy'] = csp_policy

            # 添加其他安全头
            response.headers['X-Content-Type-Options'] = 'nosniff'
            response.headers['X-Frame-Options'] = 'DENY'
            response.headers['X-XSS-Protection'] = '1; mode=block'
            response.headers['Referrer-Policy'] = 'strict-origin-when-cross-origin'

        elif 'text/css' in response.headers.get('Content-Type', ''):
            # CSS文件缓存一年
            response.headers['Cache-Control'] = 'public, max-age=31536000'
        elif 'application/javascript' in response.headers.get('Content-Type', ''):
            # JavaScript文件缓存一年
            response.headers['Cache-Control'] = 'public, max-age=31536000'
        elif 'image/' in response.headers.get('Content-Type', ''):
            # 图片文件缓存一年
            response.headers['Cache-Control'] = 'public, max-age=31536000'
        elif 'font/' in response.headers.get('Content-Type', '') or response.headers.get('Content-Type', '').endswith('font'):
            # 字体文件缓存一年
            response.headers['Cache-Control'] = 'public, max-age=31536000'
        return response

    # 确保日志目录存在
    log_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'logs')
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)

    # 配置日志 - 使用简单的控制台日志避免文件权限问题
    if not app.debug:
        # 生产环境使用文件日志（如果可能）
        try:
            import time
            from logging.handlers import RotatingFileHandler

            # 使用进程ID创建唯一的日志文件名
            process_id = os.getpid()
            log_filename = f'app_{process_id}.log'

            file_handler = RotatingFileHandler(
                os.path.join(log_dir, log_filename),
                maxBytes=1024*1024,  # 1MB
                backupCount=3,
                encoding='utf-8'
            )
            file_handler.setFormatter(logging.Formatter(
                '%(asctime)s %(levelname)s: %(message)s [in %(pathname)s:%(lineno)d]'
            ))
            file_handler.setLevel(logging.INFO)
            app.logger.addHandler(file_handler)
            app.logger.setLevel(logging.INFO)
            app.logger.info(f'应用启动 - PID: {process_id}')
        except Exception as e:
            # 文件日志失败时使用控制台日志
            app.logger.setLevel(logging.INFO)
            app.logger.info(f'文件日志配置失败，使用控制台日志: {e}')
    else:
        # 开发环境直接使用控制台日志
        app.logger.setLevel(logging.INFO)
        app.logger.info('应用启动 - 开发模式')

    return app
